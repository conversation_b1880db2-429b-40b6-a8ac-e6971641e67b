---
description:
globs:
alwaysApply: false
---
# Coding Standards

## Code Quality

### TypeScript Requirements
- All new code must use TypeScript
- Enable strict type checking
- Avoid using `any` type, prefer specific types
- Define interfaces or type aliases for complex objects

```typescript
// ✅ Good practice
interface LinkData {
  slug: string
  url: string
  createdAt: Date
  expiresAt?: Date
}

// ❌ Avoid
const linkData: any = { ... }
```

### Function and Variable Naming
- Use descriptive names
- Function names should start with verbs
- Boolean variables use `is`, `has`, `should` prefixes
- Constants use UPPER_SNAKE_CASE

```typescript
// ✅ Good practice
const isValidUrl = (url: string): boolean => { ... }
const hasExpired = computed(() => { ... })
const MAX_SLUG_LENGTH = 50

// ❌ Avoid
const check = (u: string) => { ... }
const expired = computed(() => { ... })
const maxLen = 50
```

## Security Requirements

### Input Validation
- All user input must be validated using Zod
- API endpoints must validate request parameters
- Frontend forms use vee-validate + Zod

```typescript
// API input validation
import { z } from 'zod'

const createLinkSchema = z.object({
  url: z.string().url(),
  slug: z.string().min(1).max(50).optional(),
  expiresAt: z.string().datetime().optional()
})

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const validatedData = createLinkSchema.parse(body)
  // ...
})
```

### Authentication
- Use Site Token for authentication
- Sensitive operations require permission verification
- Don't store sensitive information on the client

### Data Sanitization
- Sanitize user-input HTML content
- Validate URL format and security
- Prevent XSS and injection attacks

## Performance Best Practices

### Reactive Data
- Use `ref` and `reactive` appropriately
- Avoid unnecessary reactive wrapping
- Use `readonly` to protect data

```typescript
// ✅ Good practice
const { data: links, pending } = await $fetch('/api/links')
const filteredLinks = computed(() =>
  links.value.filter(link => link.isActive)
)

// ❌ Avoid
const links = ref([])
const filteredLinks = ref([])
watch(links, () => {
  filteredLinks.value = links.value.filter(link => link.isActive)
})
```

### Async Operations
- Use `async/await` instead of Promise chains
- Handle errors and loading states properly
- Avoid blocking UI operations

```typescript
// ✅ Good practice
const { data, error, pending } = await useFetch('/api/links')

// Error handling
try {
  const result = await $fetch('/api/link/create', { ... })
} catch (error) {
  // Handle error
}
```

## Code Organization

### File Structure
- Each file should export only one main functionality
- Group related functionality in the same directory
- Use `index.ts` as directory entry point

### Import/Export
- Use ES6 module syntax
- Organize imports alphabetically
- Separate third-party and local imports

```typescript
// Third-party libraries
import { ref, computed } from 'vue'
import { z } from 'zod'

// Local imports
import type { LinkData } from '~/types'
import { validateUrl } from '~/utils/validation'
```

### Comments and Documentation
- Add comments for complex logic
- Use JSDoc comments for public APIs
- Keep comments in sync with code

```typescript
/**
 * Create a new short link
 * @param url - Original URL
 * @param slug - Custom slug (optional)
 * @returns Created link data
 */
export async function createLink(url: string, slug?: string): Promise<LinkData> {
  // Implementation logic
}
```

## Testing Requirements

### Type Checking
- Resolve all TypeScript errors
- Use strict type configuration

### Code Style
- Follow ESLint rules
- Use Prettier for code formatting
- Automatically run lint-staged on commit

### Manual Testing
- Test main user flows
- Verify API endpoint functionality
- Check responsive design

## Git Commit Conventions

### Commit Message Format
```
type(scope): description

body (optional)

footer (optional)
```

### Commit Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation update
- `style`: Code style (no functional impact)
- `refactor`: Refactoring
- `perf`: Performance optimization
- `test`: Testing related
- `chore`: Build process or auxiliary tool changes

### Example
```
feat(api): add AI-powered slug generation

- Integrate Cloudflare AI for automatic slug creation
- Add new /api/link/ai endpoint
- Update link creation flow to support AI suggestions

Closes #123
```
