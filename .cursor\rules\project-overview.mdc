---
description:
globs:
alwaysApply: false
---
# Sink Project Overview

Sink is a simple, speedy, and secure link shortener with analytics, running 100% on Cloudflare.

## Project Architecture

This is a full-stack application built with Nuxt.js, utilizing the following tech stack:

- **Frontend Framework**: Nuxt.js 3 ([nuxt.config.ts](mdc:nuxt.config.ts))
- **UI Components**: shadcn-vue + Tailwind CSS
- **Database**: Cloudflare Workers KV
- **Analytics Engine**: Cloudflare Workers Analytics Engine
- **Deployment Platform**: Cloudflare Workers/Pages

## Core Features

1. **URL Shortening**: Compress long URLs into short links
2. **Analytics**: Monitor link click data and gather insightful statistics
3. **Custom Slugs**: Support for personalized slugs and case sensitivity
4. **AI Slug Generation**: Leverage AI to automatically generate slugs
5. **Link Expiration**: Set expiration dates for links

## Project Structure

- **app/**: Nuxt application frontend code
  - `components/`: Vue components
  - `pages/`: Page routes
  - `composables/`: Composable functions
  - `layouts/`: Layout components
  - `middleware/`: Middleware
- **server/**: Backend API code
  - `api/`: API route handlers
  - `middleware/`: Server middleware
  - `utils/`: Server utility functions
- **i18n/**: Internationalization configuration
- **schemas/**: Data validation schemas
- **scripts/**: Build scripts

## Configuration Files

- [package.json](mdc:package.json): Project dependencies and scripts
- [nuxt.config.ts](mdc:nuxt.config.ts): Nuxt configuration
- [wrangler.jsonc](mdc:wrangler.jsonc): Cloudflare Workers configuration
- [tailwind.config.js](mdc:tailwind.config.js): Tailwind CSS configuration
- [eslint.config.mjs](mdc:eslint.config.mjs): ESLint configuration
