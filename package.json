{"name": "sink", "type": "module", "version": "0.2.0", "private": true, "packageManager": "pnpm@10.11.0", "engines": {"node": ">=20.11"}, "scripts": {"dev": "nuxt dev", "build": "NODE_OPTIONS=--max-old-space-size=8192 nuxt build", "build:map": "node scripts/build-map.js", "build:colo": "node scripts/build-colo.js", "preview": "wrangler pages dev dist", "deploy": "wrangler pages deploy dist", "postinstall": "npm run build:map && nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "nuxt typecheck", "wrangler": "wrangler", "lint-staged": "lint-staged"}, "dependencies": {"@intlify/message-compiler": "^11.1.3", "@number-flow/vue": "^0.4.7", "@unovis/ts": "^1.5.1", "@unovis/vue": "^1.5.1", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.2.0", "@vueuse/motion": "^3.0.3", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "fuse.js": "^7.1.0", "globe.gl": "^2.41.4", "intl-parse-accept-language": "^1.0.0", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.511.0", "mysql-bricks": "^2.0.0", "nanoid": "^5.1.5", "pluralize": "^8.0.0", "qr-code-styling": "^1.9.2", "radix-vue": "^1.9.17", "ua-parser-js": "^2.0.3", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.0", "virtua": "^0.41.2", "vue-sonner": "^2.0.0", "vue3-simple-icons": "^13.2.0", "zod": "^3.25.20"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@nuxt/eslint": "^1.4.1", "@nuxt/eslint-config": "^1.4.1", "@nuxthub/core": "^0.8.27", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/i18n": "^9.5.4", "@nuxtjs/tailwindcss": "^6.14.0", "@vueuse/integrations": "^13.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.27.0", "lint-staged": "^16.0.0", "nuxt": "^3.17.4", "shadcn-nuxt": "^2.1.0", "simple-git-hooks": "^2.13.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vue-tsc": "^2.2.10", "wrangler": "^4.16.1"}, "simple-git-hooks": {"pre-commit": "npm run lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix"]}}