# Sink API

Writing API documentation manually can be very laborious, and we will automatically generate documents after the official release of [Nitro's OpenAPI](https://nitro.unjs.io/config#openapi).

This place provides an example of creating a short link API. Other APIs are currently available for viewing through browser developer tools.

## API Reference

### Create Short Link

```http
  POST /api/link/create
```

| Header | Description                |
| :----- | :------------------------- |
| `authorization` | `Bearer SinkCool` |
| `content-type` | `application/json` |

#### Example

```http
  POST /api/link/create
  HEADER authorization: Bearer SinkCool
  HEADER content-type: application/json
  BODY  {
          "url": "https://github.com/ccbikai/Sink/issues/14",
          "slug": "issue14"
        }
```

The BODY data must be JSON.

```http
  RESPONSE 201
  BODY  {
          "link": {
            "id": "xpqhaurv5q",
            "url": "https://github.com/ccbikai/Sink/issues/14",
            "slug": "issue14",
            "createdAt": 1718119809,
            "updatedAt": 1718119809
          }
        }
```

| Parameter | Type     | Description                |
| :-------- | :------- | :------------------------- |
| `id`     | `string` | This is automatically generated by Sink |
| `url`    | `string`   | This is confirmation of the submitted URL and is required. |
| `slug`  | `string` | This is slug generated by the system, either automatically or from the input (if provided) |
| `createdAt`     | `timestamp` | This is automatically generated with a UNIX Timestamp. |
| `updatedAt`     | `timestamp` | This is automatically generated with a UNIX Timestamp. |
