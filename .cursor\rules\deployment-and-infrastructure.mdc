---
description:
globs:
alwaysApply: false
---
# Deployment and Infrastructure

## Cloudflare Platform

Sink runs entirely on the Cloudflare platform, leveraging the following services:

### Cloudflare Workers
- Serverless computing platform
- Global edge network deployment
- Configuration file: [wrangler.jsonc](mdc:wrangler.jsonc)

### Cloudflare Pages
- Static website hosting
- Automatic build and deployment
- Preview deployment support

### Cloudflare Workers KV
- Key-value storage database
- Globally distributed storage
- Used for storing link data and configuration

### Cloudflare Analytics Engine
- Real-time analytics data collection
- High-performance time-series database
- Used for link click statistics

### Cloudflare AI
- AI model inference service
- Used for automatic slug generation
- Model: `@cf/meta/llama-3.1-8b-instruct`

## Deployment Configuration

### Environment Variables
Defined in `runtimeConfig` in [nuxt.config.ts](mdc:nuxt.config.ts):

```typescript
runtimeConfig: {
  siteToken: 'SinkCool',           // Site access token
  redirectStatusCode: '301',        // Redirect status code
  linkCacheTtl: 60,                // Link cache time
  redirectWithQuery: false,         // Whether to preserve query parameters
  homeURL: '',                     // Home URL
  cfAccountId: '',                 // Cloudflare Account ID
  cfApiToken: '',                  // Cloudflare API token
  dataset: 'sink',                 // Analytics dataset name
  aiModel: '@cf/meta/llama-3.1-8b-instruct', // AI model
  caseSensitive: false,            // Case sensitivity
  listQueryLimit: 500,             // List query limit
  disableBotAccessLog: false,      // Disable bot access logging
}
```

### Build Scripts
- `pnpm build`: Build for production
- `pnpm build:map`: Build map data
- `pnpm build:colo`: Build Cloudflare data center information

### Deployment Commands
```bash
# Preview deployment
pnpm preview

# Production deployment
pnpm deploy

# Using Wrangler CLI
wrangler pages deploy dist
```

## Performance Optimization

### Caching Strategy
- Static assets cached using Cloudflare CDN
- Configurable link data cache TTL
- Leverage edge computing to reduce latency

### Route Rules
Configured in [nuxt.config.ts](mdc:nuxt.config.ts):

```typescript
routeRules: {
  '/': {
    prerender: true,              // Prerender homepage
  },
  '/dashboard/**': {
    prerender: true,              // Prerender dashboard
    ssr: false,                   // Disable server-side rendering
  },
  '/dashboard': {
    redirect: '/dashboard/links', // Redirect to links page
  },
}
```

### Build Optimization
- Use `NODE_OPTIONS=--max-old-space-size=8192` to increase memory limit
- Enable Nuxt's built-in optimization features
- Code splitting and lazy loading

## Monitoring and Logging

### Analytics Engine
- Real-time click data collection
- Geographic location statistics
- User agent analysis
- Referrer statistics

### Error Handling
- Unified error response format
- Error logging
- Performance monitoring

### Security
- Site Token authentication
- CORS configuration
- Input validation and sanitization
- Malicious link prevention

## Development Environment

### Local Development
```bash
# Start development server
pnpm dev

# Code style checking
pnpm lint
```

### Environment Configuration
- Use `.env` file for local environment variables
- Use Cloudflare Workers local simulator during development
- Support hot reload and live preview
