---
description:
globs:
alwaysApply: false
---
# Development Guidelines

## Code Style and Standards

### TypeScript/JavaScript
- Use TypeScript for type-safe development
- Follow ESLint rules defined in [eslint.config.mjs](mdc:eslint.config.mjs)
- Use `@antfu/eslint-config` as the base configuration
- Run `pnpm lint` to check code style, `pnpm lint:fix` to auto-fix issues

### Vue Components
- Use Composition API with `<script setup>` syntax
- Name component files using PascalCase
- Place UI components in `app/components/ui/` directory
- Place business components in `app/components/` directory

### API Development
- Place API routes in `server/api/` directory
- Use Nitro's file-system routing
- Use consistent error handling format for API responses
- Leverage Cloudflare Workers KV for data storage

## Development Tools and Scripts

### Common Commands
```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm preview      # Preview built application
pnpm deploy       # Deploy to Cloudflare
pnpm lint         # Code style checking
pnpm lint:fix     # Auto-fix code style issues
```

### Environment Configuration
- Use [wrangler.jsonc](mdc:wrangler.jsonc) to configure Cloudflare Workers
- Define environment variables in `runtimeConfig` in [nuxt.config.ts](mdc:nuxt.config.ts)
- Use `.env` file for development (not committed to version control)

## Best Practices

### Performance Optimization
- Utilize Nuxt's built-in optimization features
- Properly configure `prerender` and SSR settings
- Leverage Cloudflare's global CDN network

### Security
- Use Site Token for authentication
- Validate inputs using Zod schemas
- Follow Cloudflare Workers security best practices

### Internationalization
- Use `@nuxtjs/i18n` module
- Store translation files in [i18n/](mdc:i18n/) directory
- Support multi-language detection and switching

### Testing
- Use lint-staged to automatically check code style on commit
- Follow Git commit conventions
