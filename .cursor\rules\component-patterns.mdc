---
description:
globs:
alwaysApply: false
---
# Vue Component Design Patterns

## Component Directory Structure

### UI Components (`app/components/ui/`)
Reusable UI components based on shadcn-vue:
- Buttons, inputs, dialogs, and other fundamental components
- Follow shadcn-vue design system
- Styled with Tailwind CSS
- Support theme switching (light/dark mode)

### Business Components
- `dashboard/`: Dashboard-related components
- `home/`: Homepage-related components
- `login/`: Login-related components
- `layouts/`: Layout components
- `spark-ui/`: Special UI components

### Global Components
- [SwitchLanguage.vue](mdc:app/components/SwitchLanguage.vue): Language switching component
- [SwitchTheme.vue](mdc:app/components/SwitchTheme.vue): Theme switching component

## Component Development Standards

### Component Structure
```vue
<script setup lang="ts">
// Imports
import { ref, computed } from 'vue'

// Type definitions
interface Props {
  // props types
}

// Props and Emits
const props = defineProps<Props>()
const emit = defineEmits<{
  // event types
}>()

// Reactive data
const state = ref()

// Computed properties
const computed = computed(() => {
  // computation logic
})

// Methods
function handleAction() {
  // handler logic
}
</script>

<template>
  <!-- template -->
</template>

<style scoped>
/* styles (if needed) */
</style>
```

### Naming Conventions
- Component files use PascalCase: `MyComponent.vue`
- Internal variables use camelCase
- CSS class names use Tailwind CSS utility classes
- Event names use kebab-case

### Props and Events
```typescript
// Props type definition
interface Props {
  title: string
  isVisible?: boolean
  items: Array<Item>
}

// Events type definition
const emit = defineEmits<{
  update: [value: string]
  close: []
  select: [item: Item]
}>()
```

## State Management

### Composables (`app/composables/`)
- Use VueUse utility functions
- Create custom composables for shared state management
- Follow `use` prefix naming convention

### Reactive State
```typescript
// composables/useAuth.ts
export function useAuth() {
  const isAuthenticated = ref(false)
  const user = ref(null)

  const login = async (token: string) => {
    // login logic
  }

  const logout = () => {
    // logout logic
  }

  return {
    isAuthenticated: readonly(isAuthenticated),
    user: readonly(user),
    login,
    logout
  }
}
```

## Styling and Theming

### Tailwind CSS
- Use Tailwind utility classes for styling
- Configuration file: [tailwind.config.js](mdc:tailwind.config.js)
- Support responsive design and dark mode

### Theme System
- Use `@nuxtjs/color-mode` module
- Support automatic system theme detection
- Theme switching component: [SwitchTheme.vue](mdc:app/components/SwitchTheme.vue)

### Animation Effects
- Use `@vueuse/motion` for animations
- Use animation classes provided by `tailwindcss-animate`
- Keep animations simple and performance-friendly

## Internationalization

### Multi-language Support
- Use `@nuxtjs/i18n` module
- Language switching component: [SwitchLanguage.vue](mdc:app/components/SwitchLanguage.vue)
- Store translation files in `i18n/` directory

### Usage
```vue
<template>
  <div>
    <h1>{{ $t('welcome.title') }}</h1>
    <p>{{ $t('welcome.description') }}</p>
  </div>
</template>
```
