---
description:
globs:
alwaysApply: false
---
# API Design Patterns

## API Route Structure

### Link Management API
Located in the `server/api/link/` directory, containing the following endpoints:

- [create.post.ts](mdc:server/api/link/create.post.ts): Create new short links
- [edit.put.ts](mdc:server/api/link/edit.put.ts): Edit existing links
- [delete.post.ts](mdc:server/api/link/delete.post.ts): Delete links
- [list.get.ts](mdc:server/api/link/list.get.ts): Get link lists
- [query.get.ts](mdc:server/api/link/query.get.ts): Query specific links
- [search.get.ts](mdc:server/api/link/search.get.ts): Search links
- [upsert.post.ts](mdc:server/api/link/upsert.post.ts): Create or update links
- [ai.get.ts](mdc:server/api/link/ai.get.ts): AI-generated slugs

### Other APIs
- [location.ts](mdc:server/api/location.ts): Geographic location information
- [verify.ts](mdc:server/api/verify.ts): Authentication
- `stats/`: Analytics-related APIs
- `logs/`: Logging-related APIs
- `ws/`: WebSocket-related APIs

## API Design Conventions

### HTTP Methods
- `GET`: Retrieve data (queries, lists)
- `POST`: Create resources or perform operations
- `PUT`: Update resources
- `DELETE`: Remove resources

### Response Format
```typescript
// Success response
{
  success: true,
  data: any
}

// Error response
{
  success: false,
  error: string,
  code?: number
}
```

### Authentication
- Use Site Token for authentication
- Pass token in request header: `Authorization: Bearer <token>`

### Data Validation
- Use Zod for input validation
- Define validation schemas in `schemas/` directory
- Unified error handling and response format

### Cloudflare Integration
- Use Cloudflare Workers KV for data storage
- Leverage Cloudflare Analytics Engine for statistics
- Use Cloudflare AI for slug generation

## Error Handling

### Common Error Codes
- `400`: Bad request parameters
- `401`: Unauthorized access
- `403`: Insufficient permissions
- `404`: Resource not found
- `429`: Rate limit exceeded
- `500`: Internal server error

### Error Handling Pattern
```typescript
try {
  // API logic
  return { success: true, data: result }
} catch (error) {
  return createError({
    statusCode: 400,
    statusMessage: error.message
  })
}
```
